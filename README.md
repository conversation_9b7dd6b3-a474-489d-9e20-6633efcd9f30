# Enhanced AI Client Library

A powerful, feature-rich JavaScript client library for interacting with multiple AI providers including Pollinations, DeepInfra, Together AI, Puter, and HuggingFace.

## 🚀 New Powerful Features

### ⚡ Performance & Reliability
- **Automatic Retry Logic** with exponential backoff
- **Request Timeout** management with configurable timeouts
- **Rate Limiting** and concurrency control
- **Response Caching** with configurable TTL
- **Performance Metrics** tracking and monitoring
- **Health Check** utilities for provider status

### 🔄 Advanced Request Management
- **Batch Processing** for multiple requests
- **Concurrent Request Limiting** to prevent overload
- **Request Queuing** system
- **Event-Driven Architecture** for monitoring
- **Debug Logging** for troubleshooting

### 💬 Conversation Management
- **Conversation Class** for maintaining chat context
- **Message History** tracking
- **Streaming Support** for real-time responses
- **Conversation Export/Import** functionality
- **Metadata Tracking** (tokens, message count, etc.)

### 🎯 Utility Features
- **Prompt Templates** with variable substitution
- **Model Comparison** utilities
- **Token Estimation** for cost planning
- **Cost Estimation** with pricing data
- **Enhanced Error Handling** with detailed messages

## 📦 Installation

```javascript
import { Client, Conversation, PromptTemplate, RateLimiter } from './client.js';
```

## 🛠️ Basic Usage

### Enhanced Client Configuration

```javascript
const client = new Client({
    timeout: 60000,           // 60 second timeout
    retryAttempts: 5,         // Retry failed requests 5 times
    retryDelay: 2000,         // 2 second delay between retries
    debug: true,              // Enable debug logging
    cacheEnabled: true,       // Enable response caching
    cacheTTL: 600000,         // 10 minute cache TTL
    maxConcurrentRequests: 3, // Limit concurrent requests
    rateLimitDelay: 1000      // Rate limiting delay
});
```

### Event Monitoring

```javascript
// Monitor request lifecycle
client.on('requestStart', (data) => {
    console.log('Request started:', data.params.model);
});

client.on('requestSuccess', (data) => {
    console.log(`Request completed in ${data.responseTime}ms`);
});

client.on('cacheHit', (data) => {
    console.log('Cache hit for request');
});

client.on('requestError', (data) => {
    console.error('Request failed:', data.error.message);
});
```

## 💬 Conversation Management

```javascript
// Create a conversation with system prompt
const conversation = await client.createConversation(
    "You are a helpful AI assistant specialized in JavaScript programming."
);

// Send messages and maintain context
const response1 = await conversation.send("How do I create a class in JavaScript?");
const response2 = await conversation.send("Can you show me an example with inheritance?");

// Stream responses for real-time interaction
for await (const chunk of conversation.sendStream("Explain async/await")) {
    if (chunk.choices?.[0]?.delta?.content) {
        process.stdout.write(chunk.choices[0].delta.content);
    }
}

// Export conversation history
const history = conversation.export();
console.log('Conversation metadata:', history.metadata);
```

## 🔄 Batch Processing

```javascript
const requests = [
    { model: 'gpt-4.1', messages: [{ role: 'user', content: 'What is 2+2?' }] },
    { model: 'gpt-4.1', messages: [{ role: 'user', content: 'What is the capital of France?' }] },
    { model: 'gpt-4.1', messages: [{ role: 'user', content: 'Explain quantum computing.' }] }
];

const results = await client.batchCompletion(requests, {
    concurrency: 2,           // Process 2 requests at a time
    delayBetweenBatches: 500  // 500ms delay between batches
});

results.forEach((result, index) => {
    if (result.success) {
        console.log(`Request ${index + 1}: ${result.result.choices[0].message.content}`);
    } else {
        console.log(`Request ${index + 1} failed: ${result.error.message}`);
    }
});
```

## 🎯 Model Comparison

```javascript
const prompt = "Explain the concept of recursion in programming";
const models = ['gpt-4.1', 'deepseek-v3', 'phi-4'];

const comparison = await client.compareModels(prompt, models, {
    max_tokens: 150,
    temperature: 0.7
});

comparison.forEach(result => {
    console.log(`--- ${result.model} ---`);
    if (result.success) {
        console.log(result.response);
        console.log(`Response time: ${result.responseTime}ms`);
    } else {
        console.log(`Error: ${result.error}`);
    }
});
```

## 📝 Prompt Templates

```javascript
const template = new PromptTemplate(`
You are a {{role}} assistant. 
The user's name is {{name}} and they are {{age}} years old.
Please help them with: {{task}}
Be {{tone}} in your response.
`);

const variables = {
    role: 'coding',
    name: 'Alice',
    age: 25,
    task: 'learning JavaScript',
    tone: 'encouraging and detailed'
};

// Validate template variables
const validation = template.validate(variables);
if (validation.valid) {
    const prompt = template.render(variables);
    console.log('Rendered prompt:', prompt);
}
```

## 📊 Performance Monitoring

```javascript
// Get performance metrics
const metrics = client.getMetrics();
console.log('Performance metrics:', {
    totalRequests: metrics.totalRequests,
    successRate: `${((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(2)}%`,
    averageResponseTime: `${metrics.averageResponseTime.toFixed(2)}ms`,
    cacheSize: client.getCacheSize()
});

// Health check
const health = await client.healthCheck();
console.log('Service health:', health);

// Reset metrics
client.resetMetrics();

// Clear cache
client.clearCache();
```

## 🏭 Enhanced Providers

### Together AI with Rate Limiting

```javascript
const together = new Together({
    apiKey: 'your-together-api-key',
    maxRequestsPerMinute: 30,
    debug: true,
    timeout: 120000
});
```

### HuggingFace with Auto Model Resolution

```javascript
const hf = new HuggingFace({
    apiKey: 'your-hf-api-key',
    timeout: 120000,
    retryAttempts: 3
});
```

## 🎨 Enhanced Image Generation

```javascript
const imageResponse = await client.images.generate({
    prompt: "A beautiful sunset over mountains",
    size: "1024x1024",
    model: "gptimage",
    // Enhanced error handling and caching included
});

console.log('Generated image URL:', imageResponse.data[0].url);
```

## 🔧 Utility Functions

```javascript
// Token estimation
const tokens = client.estimateTokens("Your text here");

// Cost estimation
const cost = client.estimateCost('gpt-4', 1000, 500); // input tokens, output tokens

// Rate limiter
const rateLimiter = new RateLimiter(10, 60000); // 10 requests per minute
await rateLimiter.checkLimit();
```

## 📋 Available Models

The library includes extensive model aliases for easy access:

- **OpenAI**: `gpt-4.1`, `gpt-4.1-mini`, `o3`
- **DeepSeek**: `deepseek-v3`, `deepseek-r1`
- **Anthropic**: `claude-3`
- **Meta**: `llama-3.1-8b`, `llama-3.1-70b`, `llama-4-scout`
- **Google**: `gemma-3-27b`
- **Mistral**: `mistral-small-3.1`, `mixtral-8x7b`
- **Image Models**: `flux`, `sdxl-turbo`, `gptimage`

## 🚨 Error Handling

The enhanced client provides detailed error information:

```javascript
try {
    const response = await client.chat.completions.create({...});
} catch (error) {
    console.error('Request failed:', {
        message: error.message,
        status: error.status,
        provider: client.baseUrl
    });
}
```

## 📈 Performance Tips

1. **Enable Caching**: Reduces redundant API calls
2. **Use Batch Processing**: More efficient for multiple requests
3. **Set Appropriate Timeouts**: Prevent hanging requests
4. **Monitor Metrics**: Track performance and optimize
5. **Use Rate Limiting**: Prevent hitting API limits
6. **Enable Debug Mode**: For troubleshooting during development

## 🔗 API Compatibility

The enhanced client maintains full compatibility with OpenAI's API format while adding powerful new features. All existing code will continue to work with the enhanced version.

## 📄 License

MIT License - see LICENSE file for details.
