class Client {
    constructor(options = {}) {
        this.defaultModel = options.defaultModel || null;
        this.timeout = options.timeout || 30000; // 30 second default timeout
        this.retryAttempts = options.retryAttempts || 3;
        this.retryDelay = options.retryDelay || 1000; // 1 second default retry delay
        this.debug = options.debug || false;
        this.rateLimitDelay = options.rateLimitDelay || 1000; // Rate limiting delay
        this.maxConcurrentRequests = options.maxConcurrentRequests || 5;
        this.activeRequests = 0;
        this.requestQueue = [];
        this.cache = new Map(); // Simple in-memory cache
        this.cacheEnabled = options.cacheEnabled !== false; // Default to enabled
        this.cacheTTL = options.cacheTTL || 300000; // 5 minutes default cache TTL

        if (options.baseUrl) {
            this.baseUrl = options.baseUrl;
            this.apiEndpoint = `${this.baseUrl}/chat/completions`
            this.imageEndpoint = `${this.baseUrl}/images/generations`
        } else {
            this.baseUrl = 'https://text.pollinations.ai';
            this.apiEndpoint = `${this.baseUrl}/openai`;
            this.imageEndpoint = `https://image.pollinations.ai/prompt/{prompt}`;
            this.referrer = options.referrer || 'https://g4f.dev';
            if (typeof process !== 'undefined' && process.env.POLLINATIONS_API_KEY) {
                options.apiKey = process.env.POLLINATIONS_API_KEY;
            }
        }
        this.apiKey = options.apiKey;
        this.extraHeaders = {
            'Content-Type': 'application/json',
            'User-Agent': options.userAgent || 'AI-Client/1.0',
            ...(this.apiKey ? { 'Authorization': `Bearer ${this.apiKey}` } : {}),
            ...(options.extraHeaders || {})
        };
        this.modelAliases = options.modelAliases || (!options.baseUrl ? {
          "deepseek-v3": "deepseek",
          "deepseek-r1": "deepseek-reasoning",
          "grok-3-mini-high": "grok",
          "llama-4-scout": "llamascout",
          "mistral-small-3.1": "mistral",
          "gpt-4.1-mini": "openai",
          "gpt-4o-audio": "openai-audio",
          "gpt-4.1-nano": "openai-fast",
          "gpt-4.1": "openai-large",
          "o3": "openai-reasoning",
          "gpt-4o-mini": "openai-roblox",
          "phi-4": "phi",
          "qwen2.5-coder": "qwen-coder",
          "gpt-4o-mini-search": "searchgpt",
          "gpt-image": "gptimage",
          "sdxl-turbo": "turbo",
        } : {});
        this.swapAliases = {}
        Object.keys(this.modelAliases).forEach(key => {
          this.swapAliases[this.modelAliases[key]] = key;
        });

        // Event emitter functionality
        this.events = {};

        // Performance metrics
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            totalResponseTime: 0
        };
    }

    // Event emitter methods
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }

    // Cache methods
    _getCacheKey(params) {
        return JSON.stringify(params);
    }

    _getFromCache(key) {
        if (!this.cacheEnabled) return null;
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
            return cached.data;
        }
        if (cached) {
            this.cache.delete(key);
        }
        return null;
    }

    _setCache(key, data) {
        if (!this.cacheEnabled) return;
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    // Rate limiting and concurrency control
    async _waitForSlot() {
        return new Promise((resolve) => {
            if (this.activeRequests < this.maxConcurrentRequests) {
                this.activeRequests++;
                resolve();
            } else {
                this.requestQueue.push(resolve);
            }
        });
    }

    _releaseSlot() {
        this.activeRequests--;
        if (this.requestQueue.length > 0) {
            const next = this.requestQueue.shift();
            this.activeRequests++;
            next();
        }
    }

    // Enhanced retry logic with exponential backoff
    async _retryRequest(requestFn, attempt = 1) {
        try {
            return await requestFn();
        } catch (error) {
            if (attempt >= this.retryAttempts) {
                throw error;
            }

            const delay = this.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
            if (this.debug) {
                console.log(`Request failed, retrying in ${delay}ms (attempt ${attempt}/${this.retryAttempts})`);
            }

            await new Promise(resolve => setTimeout(resolve, delay));
            return this._retryRequest(requestFn, attempt + 1);
        }
    }

    get chat() {
        return {
            completions: {
            create: async (params) => {
                const startTime = Date.now();
                this.metrics.totalRequests++;

                try {
                    // Check cache first
                    const cacheKey = this._getCacheKey(params);
                    const cached = this._getFromCache(cacheKey);
                    if (cached && !params.stream) {
                        this.emit('cacheHit', { params, cached });
                        return cached;
                    }

                    if (params.model && this.modelAliases[params.model]) {
                      params.model = this.modelAliases[params.model];
                    } else if (!params.model && this.defaultModel) {
                      params.model = this.defaultModel;
                    }
                    if (this.referrer) {
                        params.referrer = this.referrer;
                    }

                    // Wait for available slot
                    await this._waitForSlot();

                    const requestOptions = {
                        method: 'POST',
                        headers: this.extraHeaders,
                        body: JSON.stringify(params)
                    };

                    this.emit('requestStart', { params, requestOptions });

                    let result;
                    if (params.stream) {
                        result = this._streamCompletion(this.apiEndpoint, requestOptions);
                    } else {
                        result = await this._retryRequest(() =>
                            this._regularCompletion(this.apiEndpoint, requestOptions)
                        );

                        // Cache non-streaming results
                        this._setCache(cacheKey, result);
                    }

                    const responseTime = Date.now() - startTime;
                    this.metrics.successfulRequests++;
                    this.metrics.totalResponseTime += responseTime;
                    this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.successfulRequests;

                    this.emit('requestSuccess', { params, result, responseTime });
                    return result;

                } catch (error) {
                    const responseTime = Date.now() - startTime;
                    this.metrics.failedRequests++;
                    this.emit('requestError', { params, error, responseTime });
                    throw error;
                } finally {
                    this._releaseSlot();
                }
            }
            }
        };
    }

    get models() {
      return {
        list: async () => {
          const cacheKey = 'models_list';
          const cached = this._getFromCache(cacheKey);
          if (cached) {
            return cached;
          }

          const response = await this._retryRequest(() =>
            fetch(`${this.baseUrl}/models`, {
              method: 'GET',
              headers: this.extraHeaders,
              signal: AbortSignal.timeout(this.timeout)
            })
          );

          if (!response.ok) {
            throw new Error(`Failed to fetch models: ${response.status}`);
          }

          let data = await response.json();
          data = data.data || data;
          data.forEach((model, index) => {
            if (!model.id) {
              model.id = this.swapAliases[model.name] || model.name;
              data[index] = model;
            }
          });

          this._setCache(cacheKey, data);
          return data;
        }
      };
    }

    get images() {
        return {
            generate: async (params) => {
                const startTime = Date.now();
                this.metrics.totalRequests++;

                try {
                    // Check cache first
                    const cacheKey = this._getCacheKey(params);
                    const cached = this._getFromCache(cacheKey);
                    if (cached) {
                        this.emit('cacheHit', { params, cached });
                        return cached;
                    }

                    if (params.model && this.modelAliases[params.model]) {
                        params.model = this.modelAliases[params.model];
                    }

                    await this._waitForSlot();
                    this.emit('requestStart', { params, type: 'image' });

                    let result;
                    if (this.imageEndpoint.includes('{prompt}')) {
                        result = await this._retryRequest(() =>
                            this._defaultImageGeneration(params, { headers: this.extraHeaders })
                        );
                    } else {
                        result = await this._retryRequest(() =>
                            this._regularImageGeneration(params, { headers: this.extraHeaders })
                        );
                    }

                    const responseTime = Date.now() - startTime;
                    this.metrics.successfulRequests++;
                    this.metrics.totalResponseTime += responseTime;
                    this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.successfulRequests;

                    this._setCache(cacheKey, result);
                    this.emit('requestSuccess', { params, result, responseTime, type: 'image' });
                    return result;

                } catch (error) {
                    const responseTime = Date.now() - startTime;
                    this.metrics.failedRequests++;
                    this.emit('requestError', { params, error, responseTime, type: 'image' });
                    throw error;
                } finally {
                    this._releaseSlot();
                }
            }
        };
    }

    // Enhanced request methods with timeout and better error handling
    async _regularCompletion(apiEndpoint, requestOptions) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(apiEndpoint, {
                ...requestOptions,
                signal: controller.signal
            });

            if (!response.ok) {
                const errorText = await response.text().catch(() => 'Unknown error');
                throw new Error(`API request failed with status ${response.status}: ${errorText}`);
            }

            return await response.json();
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(`Request timeout after ${this.timeout}ms`);
            }
            throw error;
        } finally {
            clearTimeout(timeoutId);
        }
    }

    async *_streamCompletion(apiEndpoint, requestOptions) {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      try {
        const response = await fetch(apiEndpoint, {
          ...requestOptions,
          signal: controller.signal
        });

        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Unknown error');
          throw new Error(`API request failed with status ${response.status}: ${errorText}`);
        }

        if (!response.body) {
          throw new Error('Streaming not supported in this environment');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let chunkCount = 0;

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            chunkCount++;

            const parts = buffer.split('\n');
            buffer = parts.pop();

            for (const part of parts) {
              if (!part.trim()) continue;
              if (part === 'data: [DONE]') continue;

              try {
                if (part.startsWith('data: ')) {
                  const data = JSON.parse(part.slice(6));
                  this.emit('streamChunk', { data, chunkCount });
                  yield data;
                }
              } catch (err) {
                if (this.debug) {
                  console.error('Error parsing chunk:', part, err);
                }
                this.emit('streamError', { part, error: err });
              }
            }
          }
        } finally {
          reader.releaseLock();
          this.emit('streamEnd', { chunkCount });
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          throw new Error(`Stream timeout after ${this.timeout}ms`);
        }
        throw error;
      } finally {
        clearTimeout(timeoutId);
      }
    }

    // Utility methods
    _normalizeMessages(messages) {
      return messages.map(message => ({
        role: message.role,
        content: message.content
      }));
    }

    // Performance and monitoring methods
    getMetrics() {
        return { ...this.metrics };
    }

    resetMetrics() {
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            totalResponseTime: 0
        };
    }

    clearCache() {
        this.cache.clear();
    }

    getCacheSize() {
        return this.cache.size;
    }

    // Batch processing methods
    async batchCompletion(requests, options = {}) {
        const { concurrency = 3, delayBetweenBatches = 100 } = options;
        const results = [];

        for (let i = 0; i < requests.length; i += concurrency) {
            const batch = requests.slice(i, i + concurrency);
            const batchPromises = batch.map(async (request, index) => {
                try {
                    const result = await this.chat.completions.create(request);
                    return { success: true, result, index: i + index, request };
                } catch (error) {
                    return { success: false, error, index: i + index, request };
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);

            // Delay between batches to avoid rate limiting
            if (i + concurrency < requests.length && delayBetweenBatches > 0) {
                await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
            }
        }

        return results;
    }

    // Enhanced conversation methods
    async createConversation(systemPrompt = null) {
        return new Conversation(this, systemPrompt);
    }

    // Model comparison utility
    async compareModels(prompt, models, options = {}) {
        const requests = models.map(model => ({
            model,
            messages: [{ role: 'user', content: prompt }],
            ...options
        }));

        const results = await this.batchCompletion(requests);
        return results.map(result => ({
            model: result.request.model,
            success: result.success,
            response: result.success ? result.result.choices[0].message.content : null,
            error: result.error?.message || null,
            responseTime: result.responseTime || 0
        }));
    }

    // Token estimation utility (rough estimation)
    estimateTokens(text) {
        // Rough estimation: ~4 characters per token for English text
        return Math.ceil(text.length / 4);
    }

    // Cost estimation utility (requires model pricing data)
    estimateCost(model, inputTokens, outputTokens = 0) {
        // This would need to be populated with actual pricing data
        const pricing = {
            'gpt-4': { input: 0.03, output: 0.06 }, // per 1K tokens
            'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
            'claude-3': { input: 0.015, output: 0.075 },
            // Add more models as needed
        };

        const modelPricing = pricing[model] || { input: 0.001, output: 0.002 }; // Default fallback
        return (inputTokens / 1000 * modelPricing.input) + (outputTokens / 1000 * modelPricing.output);
    }

    // Health check utility
    async healthCheck() {
        try {
            const startTime = Date.now();
            await this.models.list();
            const responseTime = Date.now() - startTime;

            return {
                status: 'healthy',
                responseTime,
                timestamp: new Date().toISOString(),
                baseUrl: this.baseUrl
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString(),
                baseUrl: this.baseUrl
            };
        }
    }

    // Prompt template utility
    createTemplate(template) {
        return new PromptTemplate(template);
    }

    async _defaultImageGeneration(params, requestOptions) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            params = {...params};
            let prompt = params.prompt ? params.prompt : '';
            prompt = encodeURIComponent(prompt.replaceAll(" ", "+"));
            delete params.prompt;
            if (params.nologo === undefined) {
                params.nologo = true;
            }
            if (params.size) {
                [params.width, params.height] = params.size.split('x');
                delete params.size;
            }
            const encodedParams = new URLSearchParams(params);
            let url = this.imageEndpoint.replace('{prompt}', prompt);
            url += '?' + encodedParams.toString();

            const response = await fetch(url, {
                ...requestOptions,
                signal: controller.signal
            });

            if (!response.ok) {
                const errorText = await response.text().catch(() => 'Unknown error');
                throw new Error(`Image generation request failed with status ${response.status}: ${errorText}`);
            }

            return {data: [{url: response.url}]}
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(`Image generation timeout after ${this.timeout}ms`);
            }
            throw error;
        } finally {
            clearTimeout(timeoutId);
        }
    }

    async _regularImageGeneration(params, requestOptions) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(this.imageEndpoint, {
                method: 'POST',
                body: JSON.stringify(params),
                signal: controller.signal,
                ...requestOptions
            });

            if (!response.ok) {
                const errorText = await response.text().catch(() => 'Unknown error');
                throw new Error(`Image generation request failed with status ${response.status}: ${errorText}`);
            }

            return await response.json();
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(`Image generation timeout after ${this.timeout}ms`);
            }
            throw error;
        } finally {
            clearTimeout(timeoutId);
        }
    }
}

// Prompt template utility class
class PromptTemplate {
    constructor(template) {
        this.template = template;
        this.variables = this._extractVariables(template);
    }

    _extractVariables(template) {
        const matches = template.match(/\{\{(\w+)\}\}/g);
        return matches ? matches.map(match => match.slice(2, -2)) : [];
    }

    render(variables = {}) {
        let rendered = this.template;
        for (const [key, value] of Object.entries(variables)) {
            rendered = rendered.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value);
        }
        return rendered;
    }

    getVariables() {
        return [...this.variables];
    }

    validate(variables = {}) {
        const missing = this.variables.filter(variable => !(variable in variables));
        return {
            valid: missing.length === 0,
            missing
        };
    }
}

// Conversation management class
class Conversation {
    constructor(client, systemPrompt = null) {
        this.client = client;
        this.messages = [];
        this.metadata = {
            created: Date.now(),
            totalTokens: 0,
            messageCount: 0
        };

        if (systemPrompt) {
            this.messages.push({ role: 'system', content: systemPrompt });
        }
    }

    addMessage(role, content) {
        this.messages.push({ role, content });
        this.metadata.messageCount++;
        return this;
    }

    addUser(content) {
        return this.addMessage('user', content);
    }

    addAssistant(content) {
        return this.addMessage('assistant', content);
    }

    addSystem(content) {
        return this.addMessage('system', content);
    }

    async send(content, options = {}) {
        if (content) {
            this.addUser(content);
        }

        const response = await this.client.chat.completions.create({
            messages: this.messages,
            ...options
        });

        const assistantMessage = response.choices[0].message.content;
        this.addAssistant(assistantMessage);

        if (response.usage) {
            this.metadata.totalTokens += response.usage.total_tokens || 0;
        }

        return response;
    }

    async *sendStream(content, options = {}) {
        if (content) {
            this.addUser(content);
        }

        let fullResponse = '';
        const stream = await this.client.chat.completions.create({
            messages: this.messages,
            stream: true,
            ...options
        });

        for await (const chunk of stream) {
            if (chunk.choices?.[0]?.delta?.content) {
                fullResponse += chunk.choices[0].delta.content;
            }
            yield chunk;
        }

        this.addAssistant(fullResponse);
        return fullResponse;
    }

    getHistory() {
        return [...this.messages];
    }

    getMetadata() {
        return { ...this.metadata };
    }

    clear() {
        const systemMessages = this.messages.filter(m => m.role === 'system');
        this.messages = systemMessages;
        this.metadata.messageCount = systemMessages.length;
        return this;
    }

    export() {
        return {
            messages: this.getHistory(),
            metadata: this.getMetadata()
        };
    }

    import(data) {
        this.messages = data.messages || [];
        this.metadata = { ...this.metadata, ...data.metadata };
        return this;
    }
}

// Rate limiter utility class
class RateLimiter {
    constructor(maxRequests = 10, windowMs = 60000) {
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
        this.requests = [];
    }

    async checkLimit() {
        const now = Date.now();
        this.requests = this.requests.filter(time => now - time < this.windowMs);

        if (this.requests.length >= this.maxRequests) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = this.windowMs - (now - oldestRequest);
            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.checkLimit();
        }

        this.requests.push(now);
        return true;
    }
}

class DeepInfra extends Client {
    constructor(options = {}) {
        super({
            baseUrl: 'https://api.deepinfra.com/v1/openai',
            defaultModel: 'deepseek-ai/DeepSeek-V3-0324',
            ...options
        });
    }
}

class Together extends Client {
    constructor(options = {}) {
        super({
            baseUrl: 'https://api.together.xyz/v1',
            defaultModel: 'blackbox/meta-llama-3-1-8b',
            modelAliases: {
                "flux": "black-forest-labs/FLUX.1-schnell-Free",
                "llama-3.1-8b": "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
                "llama-3.1-70b": "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo",
                "llama-3.1-405b": "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
                "qwen-2.5-7b": "Qwen/Qwen2.5-7B-Instruct-Turbo",
                "qwen-2.5-72b": "Qwen/Qwen2.5-72B-Instruct-Turbo",
                "deepseek-v2.5": "deepseek-ai/deepseek-llm-67b-chat",
                "mixtral-8x7b": "mistralai/Mixtral-8x7B-Instruct-v0.1",
                "mixtral-8x22b": "mistralai/Mixtral-8x22B-Instruct-v0.1",
                ...options.modelAliases
            },
            ...options
        });
        this.rateLimiter = new RateLimiter(options.maxRequestsPerMinute || 60, 60000);
    }

    async getApiKey() {
        if (!this.apiKey) {
            if (typeof process !== 'undefined' && process.env.TOGETHER_API_KEY) {
                this.apiKey = process.env.TOGETHER_API_KEY;
                return this.apiKey;
            }
            const activation_endpoint = "https://www.codegeneration.ai/activate-v2";
            const response = await fetch(activation_endpoint);
            if (!response.ok) {
                throw new Error(`Failed to fetch API key: ${response.status}`);
            }
            const data = await response.json();
            this.apiKey = data.openAIParams?.api_key;
        }
        return this.apiKey;
    }

    async _regularImageGeneration(params, requestOptions) {
        if (params.size) {
            [params.width, params.height] = params.size.split('x');
            delete params.size;
        }
        return super._regularImageGeneration(params, requestOptions);
    }
}

class Puter {
    constructor(options = {}) {
        this.defaultModel = options.defaultModel || 'gpt-4.1';
        this.puter = options.puter || this._injectPuter();
    }

    get chat() {
        return {
            completions: {
                create: async (params) => {
                    const { messages, ...options } = params;
                    if (!options.model && this.defaultModel) {
                        options.model = this.defaultModel;
                    }
                    if (options.stream) {
                        return this._streamCompletion(messages, options);
                    }
                    const response = await (await this.puter).ai.chat(messages, false, options);
                    if (response.choices == undefined && response.message !== undefined) {
                        return {
                            ...response,
                            get choices() {
                                return [{message: response.message}];
                            }
                        };
                    } else {
                        return response;
                    }
                }
            }
        };
    }

    get models() {
      return {
        list: async () => {
            const response = await fetch("https://api.puter.com/puterai/chat/models/");
            let models = await response.json();
            models = models.models;
            const blockList = ["abuse", "costly", "fake", "model-fallback-test-1"];
            models = models.filter((model) => !model.includes("/") && !blockList.includes(model));
            return models.map(model => {
                return {
                    id: model,
                    type: "chat"
                };
            });
        }
      };
    }

    async _injectPuter() {
        return new Promise((resolve, reject) => {
            if (typeof window === 'undefined') {
                reject(new Error('Puter can only be used in a browser environment'));
                return;
            }
            if (window.puter) {
                resolve(puter);
                return;
            }
            var tag = document.createElement('script');
            tag.src = "https://js.puter.com/v2/";
            tag.onload = () => {
                resolve(puter);
            }
            tag.onerror = reject;
            var firstScriptTag = document.getElementsByTagName('script')[0];
            firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
        });
    }

    async *_streamCompletion(messages, options = {}) {
        for await (const item of await ((await this.puter).ai.chat(messages, false, options))) {
          if (item.choices == undefined && item.text !== undefined) {
            yield {
                ...item,
                get choices() {
                    return [{delta: {content: item.text}}];
                }
            };
          } else {
            yield item
          }
        }
    }
}

class HuggingFace extends Client {
    constructor(options = {}) {
        if (!options.apiKey) {
            if (typeof process !== 'undefined' && process.env.HUGGINGFACE_API_KEY) {
                options.apiKey = process.env.HUGGINGFACE_API_KEY;
            } else {
                throw new Error("HuggingFace API key is required. Set it in the options or as an environment variable HUGGINGFACE_API_KEY.");
            }
        }
        super({
            baseUrl: 'https://api-inference.huggingface.co/v1',
            defaultModel: 'meta-llama/Meta-Llama-3-8B-Instruct',
            modelAliases: {
                // Chat //
                "llama-3": "meta-llama/Llama-3.3-70B-Instruct",
                "llama-3.3-70b": "meta-llama/Llama-3.3-70B-Instruct",
                "command-r-plus": "CohereForAI/c4ai-command-r-plus-08-2024",
                "deepseek-r1": "deepseek-ai/DeepSeek-R1",
                "deepseek-v3": "deepseek-ai/DeepSeek-V3",
                "qwq-32b": "Qwen/QwQ-32B",
                "nemotron-70b": "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF",
                "qwen-2.5-coder-32b": "Qwen/Qwen2.5-Coder-32B-Instruct",
                "llama-3.2-11b": "meta-llama/Llama-3.2-11B-Vision-Instruct",
                "mistral-nemo": "mistralai/Mistral-Nemo-Instruct-2407",
                "phi-3.5-mini": "microsoft/Phi-3.5-mini-instruct",
                "gemma-3-27b": "google/gemma-3-27b-it",
                // Image //
                "flux": "black-forest-labs/FLUX.1-dev",
                "flux-dev": "black-forest-labs/FLUX.1-dev",
                "flux-schnell": "black-forest-labs/FLUX.1-schnell",
                "stable-diffusion-3.5-large": "stabilityai/stable-diffusion-3.5-large",
                "sdxl-1.0": "stabilityai/stable-diffusion-xl-base-1.0",
                "sdxl-turbo": "stabilityai/sdxl-turbo",
                "sd-3.5-large": "stabilityai/stable-diffusion-3.5-large",
            },
            ...options
        });
        this.providerMapping = {
            "google/gemma-3-27b-it": {
                "hf-inference/models/google/gemma-3-27b-it": {
                    "task": "conversational",
                    "providerId": "google/gemma-3-27b-it"
                }
            }
        };
    }

    get models() {
      return {
        list: async () => {
            const response = await fetch("https://huggingface.co/api/models?inference=warm&&expand[]=inferenceProviderMapping");
            if (!response.ok) {
              throw new Error(`Failed to fetch models: ${response.status}`);
            }
            const data = await response.json();
            return data
                .filter(model => 
                    model.inferenceProviderMapping?.some(provider => 
                        provider.status === "live" && provider.task === "conversational"
                    )
                )
                .concat(Object.keys(this.providerMapping).map(model => ({
                    id: model,
                    type: "chat"
                })))
        }
      };
    }

    async _getMapping(model) {
        if (this.providerMapping[model]) {
            return this.providerMapping[model];
        }
        const response = await fetch(`https://huggingface.co/api/models/${model}?expand[]=inferenceProviderMapping`, {
            headers: this.extraHeaders
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch model mapping: ${response.status}`);
        }

        const modelData = await response.json();
        this.providerMapping[model] = modelData.inferenceProviderMapping;
        return this.providerMapping[model];
    }

    get chat() {
        return {
            completions: {
                create: async (params) => {
                    let { model, ...options } = params;

                    if (model && this.modelAliases[model]) {
                      model = this.modelAliases[model];
                    } else if (!model && this.defaultModel) {
                      model = this.defaultModel;
                    }

                    // Model resolution would go here
                    const providerMapping = await this._getMapping(model);
                    if (!providerMapping) {
                        throw new Error(`Model is not supported: ${model}`);
                    }

                    let apiBase = this.apiBase;
                    for (const providerKey in providerMapping) {
                        const apiPath = providerKey === "novita" ? 
                            "novita/v3/openai" : 
                            `${providerKey}/v1`;
                        apiBase = `https://router.huggingface.co/${apiPath}`;

                        const task = providerMapping[providerKey].task;
                        if (task !== "conversational") {
                            throw new Error(`Model is not supported: ${model} task: ${task}`);
                        }

                        model = providerMapping[providerKey].providerId;
                        break;
                    }

                    const requestOptions = {
                        method: 'POST',
                        headers: this.extraHeaders,
                        body: JSON.stringify({
                            model,
                            ...options
                        })
                    };

                    if (params.stream) {
                        return this._streamCompletion(`${apiBase}/chat/completions`, requestOptions);
                    } else {
                        return this._regularCompletion(`${apiBase}/chat/completions`, requestOptions);
                    }
                }
            }
        };
    }
}

export { Client, DeepInfra, Together, Puter, HuggingFace, Conversation, RateLimiter, PromptTemplate };
export default Client;