import { Client, Conversation, PromptTemplate, RateLimiter, Together, HuggingFace } from './client.js';

// Example 1: Basic enhanced client with powerful features
async function basicExample() {
    const client = new Client({
        timeout: 60000,           // 60 second timeout
        retryAttempts: 5,         // Retry failed requests 5 times
        retryDelay: 2000,         // 2 second delay between retries
        debug: true,              // Enable debug logging
        cacheEnabled: true,       // Enable response caching
        cacheTTL: 600000,         // 10 minute cache TTL
        maxConcurrentRequests: 3, // Limit concurrent requests
        rateLimitDelay: 1000      // Rate limiting delay
    });

    // Event listeners for monitoring
    client.on('requestStart', (data) => {
        console.log('Request started:', data.params.model);
    });

    client.on('requestSuccess', (data) => {
        console.log(`Request completed in ${data.responseTime}ms`);
    });

    client.on('cacheHit', (data) => {
        console.log('Cache hit for request');
    });

    try {
        const response = await client.chat.completions.create({
            model: 'gpt-4.1',
            messages: [{ role: 'user', content: 'Hello, world!' }]
        });
        
        console.log('Response:', response.choices[0].message.content);
        console.log('Metrics:', client.getMetrics());
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Example 2: Conversation management
async function conversationExample() {
    const client = new Client();
    const conversation = await client.createConversation(
        "You are a helpful AI assistant specialized in JavaScript programming."
    );

    // Add messages and send
    const response1 = await conversation.send("How do I create a class in JavaScript?");
    console.log('Assistant:', response1.choices[0].message.content);

    // Continue the conversation
    const response2 = await conversation.send("Can you show me an example with inheritance?");
    console.log('Assistant:', response2.choices[0].message.content);

    // Stream a response
    console.log('Streaming response:');
    for await (const chunk of conversation.sendStream("Explain async/await")) {
        if (chunk.choices?.[0]?.delta?.content) {
            process.stdout.write(chunk.choices[0].delta.content);
        }
    }

    // Export conversation history
    const history = conversation.export();
    console.log('\nConversation metadata:', history.metadata);
}

// Example 3: Batch processing
async function batchExample() {
    const client = new Client();
    
    const requests = [
        {
            model: 'gpt-4.1',
            messages: [{ role: 'user', content: 'What is 2+2?' }]
        },
        {
            model: 'gpt-4.1',
            messages: [{ role: 'user', content: 'What is the capital of France?' }]
        },
        {
            model: 'gpt-4.1',
            messages: [{ role: 'user', content: 'Explain quantum computing in one sentence.' }]
        }
    ];

    const results = await client.batchCompletion(requests, {
        concurrency: 2,
        delayBetweenBatches: 500
    });

    results.forEach((result, index) => {
        if (result.success) {
            console.log(`Request ${index + 1}: ${result.result.choices[0].message.content}`);
        } else {
            console.log(`Request ${index + 1} failed: ${result.error.message}`);
        }
    });
}

// Example 4: Model comparison
async function modelComparisonExample() {
    const client = new Client();
    
    const prompt = "Explain the concept of recursion in programming";
    const models = ['gpt-4.1', 'deepseek-v3', 'phi-4'];
    
    const comparison = await client.compareModels(prompt, models, {
        max_tokens: 150,
        temperature: 0.7
    });

    comparison.forEach(result => {
        console.log(`\n--- ${result.model} ---`);
        if (result.success) {
            console.log(result.response);
            console.log(`Response time: ${result.responseTime}ms`);
        } else {
            console.log(`Error: ${result.error}`);
        }
    });
}

// Example 5: Prompt templates
async function promptTemplateExample() {
    const client = new Client();
    
    const template = new PromptTemplate(`
You are a {{role}} assistant. 
The user's name is {{name}} and they are {{age}} years old.
Please help them with: {{task}}

Be {{tone}} in your response.
    `.trim());

    console.log('Template variables:', template.getVariables());

    const variables = {
        role: 'coding',
        name: 'Alice',
        age: 25,
        task: 'learning JavaScript',
        tone: 'encouraging and detailed'
    };

    const validation = template.validate(variables);
    if (!validation.valid) {
        console.log('Missing variables:', validation.missing);
        return;
    }

    const prompt = template.render(variables);
    console.log('Rendered prompt:', prompt);

    const response = await client.chat.completions.create({
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: prompt }]
    });

    console.log('Response:', response.choices[0].message.content);
}

// Example 6: Enhanced provider usage
async function enhancedProviderExample() {
    // Together AI with enhanced features
    const together = new Together({
        apiKey: 'your-together-api-key',
        maxRequestsPerMinute: 30,
        debug: true
    });

    // HuggingFace with automatic model resolution
    const hf = new HuggingFace({
        apiKey: 'your-hf-api-key',
        timeout: 120000 // 2 minute timeout for large models
    });

    // Health check
    const togetherHealth = await together.healthCheck();
    console.log('Together health:', togetherHealth);

    const hfHealth = await hf.healthCheck();
    console.log('HuggingFace health:', hfHealth);

    // List available models
    const togetherModels = await together.models.list();
    console.log('Together models:', togetherModels.slice(0, 5)); // Show first 5

    const hfModels = await hf.models.list();
    console.log('HuggingFace models:', hfModels.slice(0, 5)); // Show first 5
}

// Example 7: Image generation with enhanced features
async function imageGenerationExample() {
    const client = new Client();

    try {
        const imageResponse = await client.images.generate({
            prompt: "A beautiful sunset over mountains",
            size: "1024x1024",
            model: "gptimage"
        });

        console.log('Generated image URL:', imageResponse.data[0].url);
    } catch (error) {
        console.error('Image generation failed:', error.message);
    }
}

// Example 8: Performance monitoring
async function performanceExample() {
    const client = new Client({ debug: true });

    // Make several requests
    for (let i = 0; i < 5; i++) {
        try {
            await client.chat.completions.create({
                model: 'gpt-4.1',
                messages: [{ role: 'user', content: `Test message ${i + 1}` }]
            });
        } catch (error) {
            console.log(`Request ${i + 1} failed:`, error.message);
        }
    }

    // Check performance metrics
    const metrics = client.getMetrics();
    console.log('Performance metrics:', {
        totalRequests: metrics.totalRequests,
        successRate: `${((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(2)}%`,
        averageResponseTime: `${metrics.averageResponseTime.toFixed(2)}ms`,
        cacheSize: client.getCacheSize()
    });
}

// Run examples
async function runExamples() {
    console.log('=== Basic Example ===');
    await basicExample();
    
    console.log('\n=== Conversation Example ===');
    await conversationExample();
    
    console.log('\n=== Batch Example ===');
    await batchExample();
    
    console.log('\n=== Model Comparison Example ===');
    await modelComparisonExample();
    
    console.log('\n=== Prompt Template Example ===');
    await promptTemplateExample();
    
    console.log('\n=== Enhanced Provider Example ===');
    await enhancedProviderExample();
    
    console.log('\n=== Image Generation Example ===');
    await imageGenerationExample();
    
    console.log('\n=== Performance Example ===');
    await performanceExample();
}

// Uncomment to run examples
// runExamples().catch(console.error);

export {
    basicExample,
    conversationExample,
    batchExample,
    modelComparisonExample,
    promptTemplateExample,
    enhancedProviderExample,
    imageGenerationExample,
    performanceExample,
    runExamples
};
